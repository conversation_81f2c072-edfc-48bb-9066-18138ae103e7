if Code.ensure_loaded?(OpenTelemetry) do
  defmodule Sentry.OpenTelemetry.SpanProcessor do
    @moduledoc false

    @behaviour :otel_span_processor

    require OpenTelemetry.SemConv.ClientAttributes, as: ClientAttributes
    require OpenTelemetry.SemConv.Incubating.DBAttributes, as: DBAttributes
    require OpenTelemetry.SemConv.Incubating.HTTPAttributes, as: HTTPAttributes
    require OpenTelemetry.SemConv.Incubating.URLAttributes, as: URLAttributes
    require OpenTelemetry.SemConv.Incubating.MessagingAttributes, as: MessagingAttributes

    require Logger

    alias Sentry.{Transaction, OpenTelemetry.SpanStorage, OpenTelemetry.SpanRecord}
    alias Sentry.Interfaces.Span

    # This can be a no-op since we can postpone inserting the span into storage until on_end
    @impl :otel_span_processor
    def on_start(_ctx, otel_span, _config) do
      span_record = SpanRecord.new(otel_span)
      IO.puts("=== SPAN START ===")
      IO.puts("Span Name: #{span_record.name}")
      IO.puts("Span ID: #{span_record.span_id}")
      IO.puts("Parent Span ID: #{inspect(span_record.parent_span_id)}")
      IO.puts("Trace ID: #{span_record.trace_id}")
      IO.puts("Kind: #{inspect(span_record.kind)}")
      IO.puts("Attributes: #{inspect(span_record.attributes)}")
      IO.puts("Origin: #{inspect(span_record.origin)}")
      IO.puts("==================")
      otel_span
    end

    @impl :otel_span_processor
    def on_end(otel_span, _config) do
      span_record = SpanRecord.new(otel_span)

      IO.puts("=== SPAN END ===")
      IO.puts("Span Name: #{span_record.name}")
      IO.puts("Span ID: #{span_record.span_id}")
      IO.puts("Parent Span ID: #{inspect(span_record.parent_span_id)}")
      IO.puts("Trace ID: #{span_record.trace_id}")
      IO.puts("Kind: #{inspect(span_record.kind)}")
      IO.puts("Attributes: #{inspect(span_record.attributes)}")
      IO.puts("Origin: #{inspect(span_record.origin)}")
      IO.puts("Start Time: #{span_record.start_time}")
      IO.puts("End Time: #{span_record.end_time}")
      IO.puts("================")

      SpanStorage.store_span(span_record)

      if span_record.parent_span_id == nil do
        IO.puts("=== ROOT SPAN PROCESSING ===")
        child_span_records = SpanStorage.get_child_spans(span_record.span_id)
        IO.puts("Found #{length(child_span_records)} child spans:")

        Enum.each(child_span_records, fn child ->
          IO.puts("  - Child: #{child.name} (ID: #{child.span_id})")
        end)

        transaction = build_transaction(span_record, child_span_records)
        IO.puts("Built transaction: #{transaction.transaction}")
        IO.puts("Transaction spans count: #{length(transaction.spans)}")
        IO.puts("=============================")

        result =
          case Sentry.send_transaction(transaction) do
            {:ok, _id} ->
              true

            :ignored ->
              true

            :excluded ->
              true

            {:error, error} ->
              Logger.warning("Failed to send transaction to Sentry: #{inspect(error)}")
              {:error, :invalid_span}
          end

        :ok = SpanStorage.remove_root_span(span_record.span_id)

        result
      else
        true
      end
    end

    @impl :otel_span_processor
    def force_flush(_config) do
      :ok
    end

    defp build_transaction(root_span_record, child_span_records) do
      root_span = build_span(root_span_record)
      child_spans = Enum.map(child_span_records, &build_span(&1))

      Transaction.new(%{
        span_id: root_span.span_id,
        transaction: transaction_name(root_span_record),
        transaction_info: %{source: :custom},
        start_timestamp: root_span_record.start_time,
        timestamp: root_span_record.end_time,
        contexts: %{
          trace: build_trace_context(root_span_record)
        },
        spans: child_spans
      })
    end

    defp transaction_name(
           %{attributes: %{unquote(to_string(MessagingAttributes.messaging_system())) => :oban}} =
             span_record
         ) do
      span_record.attributes["oban.job.worker"]
    end

    defp transaction_name(span_record), do: span_record.name

    defp build_trace_context(span_record) do
      {op, description} = get_op_description(span_record)

      %{
        trace_id: span_record.trace_id,
        span_id: span_record.span_id,
        parent_span_id: span_record.parent_span_id,
        op: op,
        description: description,
        origin: span_record.origin,
        data: span_record.attributes
      }
    end

    defp get_op_description(
           %{
             attributes: %{
               unquote(to_string(HTTPAttributes.http_request_method())) => http_request_method
             }
           } = span_record
         ) do
      op = "http.#{span_record.kind}"

      client_address =
        Map.get(span_record.attributes, to_string(ClientAttributes.client_address()))

      url_path = Map.get(span_record.attributes, to_string(URLAttributes.url_path()))

      description =
        to_string(http_request_method) <>
          ((client_address && " from #{client_address}") || "") <>
          ((url_path && " #{url_path}") || "")

      {op, description}
    end

    defp get_op_description(
           %{attributes: %{unquote(to_string(DBAttributes.db_system())) => _db_system}} =
             span_record
         ) do
      db_query_text = Map.get(span_record.attributes, "db.statement")

      {"db", db_query_text}
    end

    defp get_op_description(%{
           attributes:
             %{unquote(to_string(MessagingAttributes.messaging_system())) => :oban} = attributes
         }) do
      {"queue.process", attributes["oban.job.worker"]}
    end

    defp get_op_description(span_record) do
      {span_record.name, span_record.name}
    end

    defp build_span(span_record) do
      {op, description} = get_op_description(span_record)

      %Span{
        op: op,
        description: description,
        start_timestamp: span_record.start_time,
        timestamp: span_record.end_time,
        trace_id: span_record.trace_id,
        span_id: span_record.span_id,
        parent_span_id: span_record.parent_span_id,
        origin: span_record.origin,
        data: Map.put(span_record.attributes, "otel.kind", span_record.kind),
        status: span_status(span_record)
      }
    end

    defp span_status(%{
           attributes: %{
             unquote(to_string(HTTPAttributes.http_response_status_code())) =>
               http_response_status_code
           }
         }) do
      to_status(http_response_status_code)
    end

    defp span_status(_span_record), do: nil

    # WebSocket upgrade spans doesn't have a HTTP status
    defp to_status(nil), do: nil

    defp to_status(status) when status in 200..299, do: "ok"

    for {status, string} <- %{
          400 => "invalid_argument",
          401 => "unauthenticated",
          403 => "permission_denied",
          404 => "not_found",
          409 => "already_exists",
          429 => "resource_exhausted",
          499 => "cancelled",
          500 => "internal_error",
          501 => "unimplemented",
          503 => "unavailable",
          504 => "deadline_exceeded"
        } do
      defp to_status(unquote(status)), do: unquote(string)
    end

    defp to_status(_any), do: "unknown_error"
  end
end
