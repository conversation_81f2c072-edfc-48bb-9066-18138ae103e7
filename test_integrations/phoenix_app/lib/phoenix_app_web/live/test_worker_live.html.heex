<div class="mx-auto max-w-2xl">
  <div class="bg-white shadow sm:rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-base font-semibold leading-6 text-gray-900">Schedule Test Worker</h3>

      <div class="mt-5">
        <.form for={@form} phx-submit="schedule" class="space-y-6">
          <div>
            <label class="block text-sm font-medium text-gray-700">Sleep Time (ms)</label>
            <div class="mt-1">
              <input type="number" name="test_job[sleep_time]" value="1000" min="0"
                     class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" />
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700">Queue</label>
            <select name="test_job[queue]" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
              <option value="default">default</option>
              <option value="background">background</option>
            </select>
          </div>

          <div class="relative flex items-start">
            <div class="flex h-6 items-center">
              <input type="checkbox" name="test_job[should_fail]" value="true"
                     class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600" />
            </div>
            <div class="ml-3 text-sm leading-6">
              <label class="font-medium text-gray-900">Should Fail</label>
            </div>
          </div>

          <div>
            <button type="submit" class="inline-flex justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
              Schedule Job
            </button>
          </div>
        </.form>
      </div>
    </div>
  </div>

  <div class="mt-8 bg-white shadow sm:rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-base font-semibold leading-6 text-gray-900">Auto Schedule Multiple Jobs</h3>

      <div class="mt-5">
        <.form for={@auto_form} phx-submit="auto_schedule" class="space-y-6">
          <div>
            <label class="block text-sm font-medium text-gray-700">Number of Jobs</label>
            <div class="mt-1">
              <input type="number"
                     name="auto[job_count]"
                     value="5"
                     min="1"
                     max="100"
                     class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" />
            </div>
            <p class="mt-2 text-sm text-gray-500">
              Jobs will be created with random sleep times (500-5000ms), random queues, and random failure states.
            </p>
          </div>

          <div>
            <button type="submit" class="inline-flex justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
              Auto Schedule Jobs
            </button>
          </div>
        </.form>
      </div>
    </div>
  </div>

  <div class="mt-8">
    <h3 class="text-base font-semibold leading-6 text-gray-900 mb-4">Recent Jobs</h3>

    <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
      <table class="min-w-full divide-y divide-gray-300">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">ID</th>
            <th class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Queue</th>
            <th class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">State</th>
            <th class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Attempt</th>
            <th class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Args</th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200 bg-white">
          <%= for job <- @jobs do %>
            <tr>
              <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500"><%= job.id %></td>
              <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500"><%= job.queue %></td>
              <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500"><%= job.state %></td>
              <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500"><%= job.attempt %></td>
              <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500"><%= inspect(job.args) %></td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
  </div>
</div>
